import axios from 'axios';
import { Session } from '/@/utils/storage';
import { ElMessage } from 'element-plus';

// 知识库相关接口

/**
 * 知识库数据类型定义
 */
export interface KnowledgeBase {
  id?: string;
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: any;
  chunk_count?: number;
  document_count?: number;
  token_num?: number;
  status?: string;
  language?: string;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  create_time?: number;
  update_time?: number;
  create_date?: string;
  update_date?: string;
  created_by?: string;
  tenant_id?: string;
}

// 类型别名，保持向后兼容
export type KnowledgeBaseInfo = KnowledgeBase;

/**
 * 创建知识库请求参数
 */
export interface CreateKnowledgeBaseParams {
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: {
    chunk_token_num?: number;
    delimiter?: string;
    html4excel?: boolean;
    layout_recognize?: string;
    auto_keywords?: number;
    auto_questions?: number;
    task_page_size?: number;
    tag_kb_ids?: string[];
    raptor?: any;
    graphrag?: any;
  };
}

/**
 * 更新知识库请求参数
 */
export interface UpdateKnowledgeBaseParams {
  name?: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: any;
}

/**
 * 知识库列表查询参数
 */
export interface KnowledgeBaseQueryParams {
  page?: number;
  page_size?: number;
  orderby?: 'create_time' | 'update_time';
  desc?: boolean;
  name?: string;
  id?: string;
}

/**
 * 知识库统计信息
 */
export interface KnowledgeBaseStats {
  total_kb: number;
  total_documents: number;
  total_chunks: number;
  total_tokens: number;
  active_kb: number;
  recent_created: number;
  storage_used: string;
  last_update: string;
}

/**
 * API 响应格式
 */
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// 创建专门用于FastAPI的axios实例
export const fastApiRequest = axios.create({
  baseURL: (() => {
    // 优先使用环境变量配置的FastAPI URL
    const fastApiUrl = import.meta.env.VITE_FASTAPI_URL;
    if (fastApiUrl) {
      return fastApiUrl;
    }

    // 开发环境使用代理
    if (import.meta.env.DEV) {
      return '/fastapi';
    }

    // 生产环境直接访问FastAPI服务
    return 'http://localhost:8000';
  })(),
  timeout: 50000,
  headers: { 'Content-Type': 'application/json' },
});

// 添加请求拦截器 - 自动添加token
fastApiRequest.interceptors.request.use(
  (config) => {
    if (Session.get('token')) {
      const token = Session.get('token');
      // 如果 token 已经包含 Bearer 前缀，直接使用；否则添加 Bearer 前缀
      config.headers!['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器 - 处理错误
fastApiRequest.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录');
    } else if (error.response?.status === 403) {
      ElMessage.error('权限不足');
    } else if (error.response?.status === 404) {
      ElMessage.error('接口不存在');
    } else {
      ElMessage.error(error.message || '请求失败');
    }
    return Promise.reject(error);
  }
);

// API基础URL通过fastApiRequest实例自动处理，无需手动管理

/**
 * 创建知识库
 */
export function createKnowledgeBase(data: CreateKnowledgeBaseParams) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/create`,
    method: 'post',
    data
  });
}

/**
 * 获取知识库列表
 */
export function getKnowledgeBaseList(params?: KnowledgeBaseQueryParams) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/list`,
    method: 'get',
    params
  });
}

/**
 * 获取知识库详情
 */
export function getKnowledgeBaseDetail(id: string) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/${id}`,
    method: 'get'
  });
}

/**
 * 更新知识库
 */
export function updateKnowledgeBase(id: string, data: UpdateKnowledgeBaseParams) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除知识库
 */
export function deleteKnowledgeBases(ids?: string[]) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/delete`,
    method: 'delete',
    data: { ids }
  });
}

/**
 * 获取知识库统计信息
 */
export function getKnowledgeBaseStats() {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/stats/overview`,
    method: 'get'
  });
}

/**
 * 知识库服务健康检查
 */
export function checkKnowledgeBaseHealth() {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/health`,
    method: 'get'
  });
}

/**
 * 批量删除知识库
 */
export function batchDeleteKnowledgeBases(ids: string[]) {
  return deleteKnowledgeBases(ids);
}

/**
 * 删除所有知识库
 */
export function deleteAllKnowledgeBases() {
  return deleteKnowledgeBases(undefined);
}

/**
 * 搜索知识库
 */
export function searchKnowledgeBases(keyword: string, params?: Omit<KnowledgeBaseQueryParams, 'name'>) {
  return getKnowledgeBaseList({
    ...params,
    name: keyword
  });
}

/**
 * 获取知识库分页列表
 */
export function getKnowledgeBasePageList(page: number = 1, pageSize: number = 30, params?: Omit<KnowledgeBaseQueryParams, 'page' | 'page_size'>) {
  return getKnowledgeBaseList({
    page,
    page_size: pageSize,
    ...params
  });
}

/**
 * 复制知识库（基于现有知识库创建新的）
 */
export async function copyKnowledgeBase(sourceId: string, newName: string) {
  try {
    const response = await getKnowledgeBaseDetail(sourceId);
    if (response.data && response.code === 0) {
      const sourceKb = response.data;
      const newKbData: CreateKnowledgeBaseParams = {
        name: newName,
        description: sourceKb.description ? `${sourceKb.description} (副本)` : '知识库副本',
        embedding_model: sourceKb.embedding_model || 'BAAI/bge-large-zh-v1.5@BAAI',
        permission: sourceKb.permission || 'me',
        chunk_method: sourceKb.chunk_method || 'naive',
        pagerank: sourceKb.pagerank || 0,
        parser_config: sourceKb.parser_config
      };
      return createKnowledgeBase(newKbData);
    }
    throw new Error('获取源知识库信息失败');
  } catch (error) {
    throw new Error('复制知识库失败');
  }
}

/**
 * 检查知识库名称是否可用
 */
export async function checkKnowledgeBaseName(name: string) {
  try {
    const response = await getKnowledgeBaseList({ name });
    if (response.data && response.code === 0) {
      return Array.isArray(response.data) ? response.data.length === 0 : true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

// 嵌入模型已固化为 BAAI/bge-large-zh-v1.5@BAAI，不再需要动态获取
// 相关函数已移除以符合架构简化方案

/**
 * 获取分块方法列表
 */
export function getChunkMethods() {
  return Promise.resolve([
    { label: '通用', value: 'naive' },
    { label: '书籍', value: 'book' },
    { label: '邮件', value: 'email' },
    { label: '法律', value: 'laws' },
    { label: '手动', value: 'manual' },
    { label: '单一', value: 'one' },
    { label: '论文', value: 'paper' },
    { label: '图片', value: 'picture' },
    { label: '演示文稿', value: 'presentation' },
    { label: '问答', value: 'qa' },
    { label: '表格', value: 'table' },
    { label: '标签', value: 'tag' }
  ]);
}
