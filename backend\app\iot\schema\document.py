#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理相关的 Schema 定义

基于 RAGFlow API 规范设计的文档数据模型
严格遵循指南中的API优先策略和参数预处理策略
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field
from fastapi import UploadFile


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    UPLOADING = "uploading"      # 上传中
    UPLOADED = "uploaded"        # 已上传
    PARSING = "parsing"          # 解析中
    PARSED = "parsed"           # 已解析
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"     # 已取消


class DocumentType(str, Enum):
    """文档类型枚举"""
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    XLSX = "xlsx"
    XLS = "xls"
    PPT = "ppt"
    PPTX = "pptx"
    CSV = "csv"
    JSON = "json"
    XML = "xml"


class DocumentInfo(BaseModel):
    """文档信息模型"""
    id: Optional[str] = Field(None, description="文档ID")
    name: str = Field(..., description="文档名称")
    type: Optional[DocumentType] = Field(None, description="文档类型")
    size: Optional[int] = Field(None, description="文件大小(字节)")
    status: Optional[DocumentStatus] = Field(None, description="文档状态")
    chunk_num: Optional[int] = Field(0, description="分块数量")
    token_num: Optional[int] = Field(0, description="Token数量")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")
    thumbnail: Optional[str] = Field(None, description="缩略图URL")
    progress: Optional[float] = Field(0.0, ge=0.0, le=1.0, description="处理进度(0-1)")
    progress_msg: Optional[str] = Field(None, description="进度消息")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")


class DocumentUpload(BaseModel):
    """文档上传请求"""
    kb_id: str = Field(..., description="知识库ID")
    parser_id: Optional[str] = Field("naive", description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")
    run_after_upload: bool = Field(True, description="上传后是否立即解析")


class DocumentUpdate(BaseModel):
    """文档更新请求"""
    name: Optional[str] = Field(None, description="文档名称")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class DocumentQuery(BaseModel):
    """文档查询参数"""
    kb_id: str = Field(..., description="知识库ID")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(30, ge=1, le=100, description="每页数量")
    orderby: str = Field("create_time", description="排序字段")
    desc: bool = Field(True, description="是否降序")
    keywords: Optional[str] = Field(None, description="关键词搜索")
    status: Optional[DocumentStatus] = Field(None, description="状态筛选")
    type: Optional[DocumentType] = Field(None, description="类型筛选")


class DocumentDelete(BaseModel):
    """文档删除请求"""
    kb_id: str = Field(..., description="知识库ID")
    doc_ids: List[str] = Field(..., description="文档ID列表")


class DocumentParseControl(BaseModel):
    """文档解析控制"""
    kb_id: str = Field(..., description="知识库ID")
    doc_id: str = Field(..., description="文档ID")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class DocumentParseStatus(BaseModel):
    """文档解析状态"""
    doc_id: str = Field(..., description="文档ID")
    status: DocumentStatus = Field(..., description="解析状态")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="解析进度")
    progress_msg: Optional[str] = Field(None, description="进度消息")
    chunk_num: int = Field(0, description="已生成分块数量")
    token_num: int = Field(0, description="已生成Token数量")
    error_msg: Optional[str] = Field(None, description="错误信息")


class DocumentList(BaseModel):
    """文档列表响应"""
    documents: List[DocumentInfo] = Field(default_factory=list, description="文档列表")
    total: int = Field(0, description="总数量")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(30, description="每页数量")
    has_more: bool = Field(False, description="是否有更多数据")


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    doc_id: str = Field(..., description="文档ID")
    name: str = Field(..., description="文档名称")
    size: int = Field(..., description="文件大小")
    status: DocumentStatus = Field(..., description="文档状态")
    message: str = Field("上传成功", description="响应消息")


class DocumentDownloadInfo(BaseModel):
    """文档下载信息"""
    doc_id: str = Field(..., description="文档ID")
    name: str = Field(..., description="文档名称")
    size: int = Field(..., description="文件大小")
    content_type: str = Field(..., description="内容类型")
    download_url: Optional[str] = Field(None, description="下载链接")


# RAGFlow API 参数预处理相关模型
class RAGFlowDocumentUpload(BaseModel):
    """RAGFlow文档上传参数（预处理后）"""
    parser_id: str = Field(default="naive", description="解析器ID")
    parser_config: Dict[str, Any] = Field(default_factory=dict, description="解析器配置")
    run: bool = Field(True, description="是否立即运行解析")


class RAGFlowDocumentUpdate(BaseModel):
    """RAGFlow文档更新参数（预处理后）"""
    name: Optional[str] = Field(None, description="文档名称")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class RAGFlowDocumentQuery(BaseModel):
    """RAGFlow文档查询参数（预处理后）"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(30, ge=1, le=100, description="每页数量")
    orderby: str = Field("create_time", description="排序字段")
    desc: bool = Field(True, description="是否降序")
    keywords: Optional[str] = Field(None, description="关键词搜索")


class RAGFlowDocumentDelete(BaseModel):
    """RAGFlow文档删除参数（预处理后）"""
    doc_ids: List[str] = Field(..., description="文档ID列表")


# 文件上传相关模型
class FileUploadProgress(BaseModel):
    """文件上传进度"""
    file_name: str = Field(..., description="文件名")
    total_size: int = Field(..., description="总大小")
    uploaded_size: int = Field(0, description="已上传大小")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="上传进度")
    speed: Optional[str] = Field(None, description="上传速度")
    remaining_time: Optional[str] = Field(None, description="剩余时间")
    status: str = Field("uploading", description="上传状态")


class FileValidation(BaseModel):
    """文件验证配置"""
    max_size: int = Field(100 * 1024 * 1024, description="最大文件大小(字节)")  # 100MB
    allowed_types: List[str] = Field(
        default_factory=lambda: [
            "pdf", "docx", "doc", "txt", "md", "html",
            "xlsx", "xls", "ppt", "pptx", "csv", "json", "xml"
        ],
        description="允许的文件类型"
    )
    allowed_mime_types: List[str] = Field(
        default_factory=lambda: [
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain",
            "text/markdown",
            "text/html",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/csv",
            "application/json",
            "application/xml",
            "text/xml"
        ],
        description="允许的MIME类型"
    )
