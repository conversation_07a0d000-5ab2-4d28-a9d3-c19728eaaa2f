1<template>
  <div class="file-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">文件管理</h2>
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>知识库管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentKnowledgeBase?.name || '文件管理' }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <div class="header-actions">
        <el-select
          v-model="selectedKbId"
          placeholder="选择知识库"
          @change="handleKbChange"
          style="width: 200px"
        >
          <el-option
            v-for="kb in knowledgeBases"
            :key="kb.id"
            :label="kb.name"
            :value="kb.id"
          />
        </el-select>

        <el-button
          type="primary"
          :icon="Upload"
          @click="showUploadDialog = true"
          :disabled="!selectedKbId"
        >
          上传文档
        </el-button>

        <el-button
          :icon="Setting"
          @click="showSettings = !showSettings"
        >
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel" :class="{ 'collapsed': leftPanelCollapsed }">
        <div class="panel-header">
          <span v-if="!leftPanelCollapsed">快速操作</span>
          <el-button
            type="text"
            :icon="leftPanelCollapsed ? Expand : Fold"
            @click="leftPanelCollapsed = !leftPanelCollapsed"
          />
        </div>

        <div v-if="!leftPanelCollapsed" class="panel-content">
          <!-- 文档统计 -->
          <el-card class="stats-card">
            <template #header>
              <span>文档统计</span>
            </template>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ documentStats.total }}</div>
                <div class="stat-label">总文档</div>
              </div>
              <div class="stat-item">
                <div class="stat-number success">{{ documentStats.parsed }}</div>
                <div class="stat-label">已解析</div>
              </div>
              <div class="stat-item">
                <div class="stat-number warning">{{ documentStats.parsing }}</div>
                <div class="stat-label">解析中</div>
              </div>
              <div class="stat-item">
                <div class="stat-number danger">{{ documentStats.failed }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="actions-card">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="action-buttons">
              <el-button
                type="primary"
                :icon="Upload"
                @click="showUploadDialog = true"
                :disabled="!selectedKbId"
                block
              >
                上传文档
              </el-button>
              <el-button
                type="success"
                :icon="VideoPlay"
                @click="startBatchParsing"
                :disabled="!canBatchParse"
                :loading="batchParsing"
                block
              >
                批量解析
              </el-button>
              <el-button
                type="warning"
                :icon="VideoPause"
                @click="stopBatchParsing"
                :disabled="!canBatchStop"
                :loading="batchStopping"
                block
              >
                停止解析
              </el-button>
              <el-button
                type="info"
                :icon="Refresh"
                @click="refreshDocuments"
                :loading="refreshing"
                block
              >
                刷新列表
              </el-button>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="activity-card">
            <template #header>
              <span>最近活动</span>
            </template>
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon :color="activity.color">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-text">{{ activity.text }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 右侧主内容 -->
      <div class="right-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="文档列表" name="list">
            <DocumentList
              ref="documentListRef"
              :knowledge-base-id="selectedKbId"
              :auto-refresh="autoRefresh"
              @document-select="handleDocumentSelect"
              @document-action="handleDocumentAction"
            />
          </el-tab-pane>

          <el-tab-pane label="解析状态" name="status">
            <DocumentParseStatus
              ref="parseStatusRef"
              :knowledge-base-id="selectedKbId"
              :documents="selectedDocuments"
              @status-change="handleStatusChange"
              @parse-complete="handleParseComplete"
              @parse-error="handleParseError"
            />
          </el-tab-pane>

          <el-tab-pane label="上传历史" name="history">
            <div class="upload-history">
              <el-table :data="uploadHistory" stripe>
                <el-table-column prop="fileName" label="文件名" />
                <el-table-column prop="size" label="大小" />
                <el-table-column prop="status" label="状态">
                  <template #default="{ row }">
                    <el-tag :type="getUploadStatusType(row.status)">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="uploadTime" label="上传时间" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文档"
      width="800px"
      :close-on-click-modal="false"
    >
      <DocumentUpload
        :knowledge-base-id="selectedKbId"
        :show-config="true"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @upload-progress="handleUploadProgress"
      />
    </el-dialog>

    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="文档预览"
      width="90%"
      :close-on-click-modal="false"
      fullscreen
    >
      <DocumentPreview
        v-if="previewDocument"
        :document="previewDocument"
        :visible="showPreviewDialog"
        :show-settings="true"
        @close="showPreviewDialog = false"
        @download="handleDocumentDownload"
      />
    </el-dialog>

    <!-- 设置面板 -->
    <el-drawer
      v-model="showSettings"
      title="文件管理设置"
      direction="rtl"
      size="400px"
    >
      <div class="settings-content">
        <el-form label-width="120px">
          <el-form-item label="自动刷新">
            <el-switch v-model="autoRefresh" />
            <div class="form-hint">开启后将自动刷新文档状态</div>
          </el-form-item>

          <el-form-item label="刷新间隔">
            <el-select v-model="refreshInterval" :disabled="!autoRefresh">
              <el-option label="5秒" :value="5000" />
              <el-option label="10秒" :value="10000" />
              <el-option label="30秒" :value="30000" />
              <el-option label="1分钟" :value="60000" />
            </el-select>
          </el-form-item>

          <el-form-item label="显示缩略图">
            <el-switch v-model="showThumbnails" />
          </el-form-item>

          <el-form-item label="每页显示">
            <el-select v-model="pageSize">
              <el-option label="10条" :value="10" />
              <el-option label="20条" :value="20" />
              <el-option label="50条" :value="50" />
              <el-option label="100条" :value="100" />
            </el-select>
          </el-form-item>

          <el-form-item label="默认解析器">
            <el-select v-model="defaultParser">
              <el-option
                v-for="parser in parserOptions"
                :key="parser.value"
                :label="parser.label"
                :value="parser.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <el-divider />

        <div class="settings-actions">
          <el-button @click="resetSettings">重置设置</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  Upload,
  Setting,
  Expand,
  Fold,
  VideoPlay,
  VideoPause,
  Refresh
} from '@element-plus/icons-vue';

// 导入组件
import DocumentUpload from '/@/components/FileUpload/DocumentUpload.vue';
import DocumentList from '/@/components/FileManagement/DocumentList.vue';
import DocumentPreview from '/@/components/FileManagement/DocumentPreview.vue';
import DocumentParseStatus from '/@/components/FileManagement/DocumentParseStatus.vue';

// 导入API
import {
  getKnowledgeBaseList,
  type KnowledgeBaseInfo
} from '/@/api/iot/knowledgeBase';
import {
  getParserOptions,
  type DocumentInfo
} from '/@/api/iot/document';

// 响应式数据
const selectedKbId = ref('');
const knowledgeBases = ref<KnowledgeBaseInfo[]>([]);
const currentKnowledgeBase = ref<KnowledgeBaseInfo | null>(null);

// UI状态
const activeTab = ref('list');
const leftPanelCollapsed = ref(false);
const showUploadDialog = ref(false);
const showPreviewDialog = ref(false);
const showSettings = ref(false);

// 文档相关
const selectedDocuments = ref<DocumentInfo[]>([]);
const previewDocument = ref<DocumentInfo | null>(null);
const uploadHistory = ref<any[]>([]);

// 操作状态
const refreshing = ref(false);
const batchParsing = ref(false);
const batchStopping = ref(false);

// 设置
const autoRefresh = ref(true);
const refreshInterval = ref(30000);
const showThumbnails = ref(true);
const pageSize = ref(20);
const defaultParser = ref('naive');

// 组件引用
const documentListRef = ref();
const parseStatusRef = ref();

// 解析器选项
const parserOptions = getParserOptions();

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    icon: 'Upload',
    color: '#409EFF',
    text: '上传了文档 "项目需求文档.pdf"',
    time: '2分钟前'
  },
  {
    id: 2,
    icon: 'Check',
    color: '#67C23A',
    text: '文档 "用户手册.docx" 解析完成',
    time: '5分钟前'
  },
  {
    id: 3,
    icon: 'Warning',
    color: '#F56C6C',
    text: '文档 "数据报告.xlsx" 解析失败',
    time: '10分钟前'
  }
]);

// 计算属性
const documentStats = computed(() => {
  const stats = {
    total: 0,
    parsed: 0,
    parsing: 0,
    failed: 0
  };

  selectedDocuments.value.forEach(doc => {
    stats.total++;
    if (doc.status === 'parsed') stats.parsed++;
    else if (doc.status === 'parsing') stats.parsing++;
    else if (doc.status === 'failed') stats.failed++;
  });

  return stats;
});

const canBatchParse = computed(() => {
  return selectedDocuments.value.some(doc =>
    doc.status === 'uploaded' || doc.status === 'failed'
  );
});

const canBatchStop = computed(() => {
  return selectedDocuments.value.some(doc => doc.status === 'parsing');
});

// 方法
const loadKnowledgeBases = async () => {
  try {
    const response = await getKnowledgeBaseList({
      page: 1,
      page_size: 100
    });

    if (response.code === 200) {
      knowledgeBases.value = response.data?.knowledge_bases || [];

      // 如果有知识库且没有选中的，默认选择第一个
      if (knowledgeBases.value.length > 0 && !selectedKbId.value) {
        const firstKb = knowledgeBases.value[0];
        if (firstKb.id) {
          selectedKbId.value = firstKb.id;
          handleKbChange(selectedKbId.value);
        }
      }
    }
  } catch (error) {
    ElMessage.error('获取知识库列表失败');
  }
};

const handleKbChange = (kbId: string) => {
  const kb = knowledgeBases.value.find(k => k.id === kbId);
  currentKnowledgeBase.value = kb || null;

  // 清空选中的文档
  selectedDocuments.value = [];

  // 刷新文档列表
  if (documentListRef.value) {
    documentListRef.value.refreshList();
  }
};

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};

const handleDocumentSelect = (documents: DocumentInfo[]) => {
  selectedDocuments.value = documents;
};

const handleDocumentAction = (action: string, document: DocumentInfo) => {
  switch (action) {
    case 'preview':
      previewDocument.value = document;
      showPreviewDialog.value = true;
      break;
    case 'download':
      handleDocumentDownload(document);
      break;
    // 其他操作...
  }
};

const handleStatusChange = (_documents: DocumentInfo[]) => {
  // 状态变化处理
};

const handleParseComplete = (document: DocumentInfo) => {
  ElNotification({
    title: '解析完成',
    message: `文档 "${document.name}" 解析完成`,
    type: 'success'
  });

  // 添加到最近活动
  recentActivities.value.unshift({
    id: Date.now(),
    icon: 'Check',
    color: '#67C23A',
    text: `文档 "${document.name}" 解析完成`,
    time: '刚刚'
  });

  // 保持最多10条记录
  if (recentActivities.value.length > 10) {
    recentActivities.value = recentActivities.value.slice(0, 10);
  }
};

const handleParseError = (document: DocumentInfo, error: string) => {
  ElNotification({
    title: '解析失败',
    message: `文档 "${document.name}" 解析失败: ${error}`,
    type: 'error'
  });

  // 添加到最近活动
  recentActivities.value.unshift({
    id: Date.now(),
    icon: 'Warning',
    color: '#F56C6C',
    text: `文档 "${document.name}" 解析失败`,
    time: '刚刚'
  });
};

const handleUploadSuccess = (files: any[]) => {
  ElMessage.success(`成功上传 ${files.length} 个文件`);
  showUploadDialog.value = false;

  // 添加到上传历史
  files.forEach(file => {
    uploadHistory.value.unshift({
      fileName: file.name,
      size: file.size,
      status: '上传成功',
      uploadTime: new Date().toLocaleString()
    });
  });

  // 添加到最近活动
  files.forEach(file => {
    recentActivities.value.unshift({
      id: Date.now() + Math.random(),
      icon: 'Upload',
      color: '#409EFF',
      text: `上传了文档 "${file.name}"`,
      time: '刚刚'
    });
  });

  // 刷新文档列表
  refreshDocuments();
};

const handleUploadError = (error: string) => {
  ElMessage.error(`上传失败: ${error}`);
};

const handleUploadProgress = (_progress: number) => {
  // 上传进度处理
};

const handleDocumentDownload = (document: DocumentInfo) => {
  ElMessage.success(`开始下载: ${document.name}`);
};

const refreshDocuments = () => {
  refreshing.value = true;

  if (documentListRef.value) {
    documentListRef.value.refreshList();
  }

  if (parseStatusRef.value) {
    parseStatusRef.value.refreshStatus();
  }

  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const startBatchParsing = () => {
  if (parseStatusRef.value) {
    parseStatusRef.value.startBatchParsing();
  }
};

const stopBatchParsing = () => {
  if (parseStatusRef.value) {
    parseStatusRef.value.stopBatchParsing();
  }
};

const getUploadStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '上传成功': 'success',
    '上传失败': 'danger',
    '上传中': 'warning'
  };
  return typeMap[status] || 'info';
};

const resetSettings = () => {
  autoRefresh.value = true;
  refreshInterval.value = 30000;
  showThumbnails.value = true;
  pageSize.value = 20;
  defaultParser.value = 'naive';

  ElMessage.success('设置已重置');
};

const saveSettings = () => {
  // 保存设置到本地存储
  const settings = {
    autoRefresh: autoRefresh.value,
    refreshInterval: refreshInterval.value,
    showThumbnails: showThumbnails.value,
    pageSize: pageSize.value,
    defaultParser: defaultParser.value
  };

  localStorage.setItem('fileManagementSettings', JSON.stringify(settings));
  ElMessage.success('设置已保存');
  showSettings.value = false;
};

const loadSettings = () => {
  const saved = localStorage.getItem('fileManagementSettings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      autoRefresh.value = settings.autoRefresh ?? true;
      refreshInterval.value = settings.refreshInterval ?? 30000;
      showThumbnails.value = settings.showThumbnails ?? true;
      pageSize.value = settings.pageSize ?? 20;
      defaultParser.value = settings.defaultParser ?? 'naive';
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }
};

// 生命周期
onMounted(() => {
  loadSettings();
  loadKnowledgeBases();
});

// 监听知识库变化
watch(selectedKbId, (newKbId) => {
  if (newKbId) {
    handleKbChange(newKbId);
  }
});
</script>

<style scoped>
.file-management-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.breadcrumb {
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel {
  width: 300px;
  background: #fff;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.left-panel.collapsed {
  width: 60px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stats-card {
  margin-bottom: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-number.success { color: #67C23A; }
.stat-number.warning { color: #E6A23C; }
.stat-number.danger { color: #F56C6C; }

.stat-label {
  font-size: 12px;
  color: #909399;
}

.actions-card {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-card {
  margin-bottom: 0;
  flex: 1;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 13px;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 11px;
  color: #909399;
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.upload-history {
  padding: 20px;
}

.settings-content {
  padding: 20px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.settings-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 250px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .main-content {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #ebeef5;
  }

  .left-panel.collapsed {
    height: 60px;
    width: 100%;
  }

  .panel-content {
    flex-direction: row;
    overflow-x: auto;
    padding: 12px;
  }

  .stats-card,
  .actions-card,
  .activity-card {
    min-width: 200px;
    margin-right: 12px;
  }

  .right-content {
    margin: 12px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .file-management-container {
    background: #1a1a1a;
  }

  .page-header,
  .left-panel,
  .right-content {
    background: #2d2d2d;
    border-color: #404040;
  }

  .page-title {
    color: #e4e7ed;
  }

  .panel-header {
    color: #e4e7ed;
    border-color: #404040;
  }

  .stat-number {
    color: #e4e7ed;
  }

  .activity-text {
    color: #e4e7ed;
  }
}

/* 动画效果 */
.left-panel {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-number {
  transition: color 0.3s ease;
}

.activity-item {
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.activity-list::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.activity-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.activity-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.activity-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}
</style>